import os
import random
import string
from fastapi.testclient import TestClient
from pprint import pprint
from main import app

email = os.environ["TEST_EMAIL"]
password = os.environ["TEST_PASSWORD"]

login_successful_info = {"email": email, "password": password}
login_fail_info = {
    "email": email,
    "password": str(random.choices(string.ascii_letters, k=10)),
}

token = ""


def test_successful_login_logout():
    with TestClient(app) as client:
        response = client.post("/auth/login", params=login_successful_info)
        pprint(f"response: {str(response)}")
        assert response.status_code == 200
        token = response.cookies.get("session")
        # client.cookies.set('session', token)
        print("token 1: " + token)
        assert client.cookies.get("session")
        assert len(client.cookies.get("session")) > 0

        # test successful logout
        response = client.post("/auth/logout")
        assert response.status_code == 200

        # check that cookie is deleted
        assert client.cookies.get("session") is None


def test_fail_login():
    with TestClient(app) as client:
        response = client.post("/auth/login", params=login_fail_info)
        pprint(f"response: {str(response)}")
        assert response.status_code == 401


# import string
# import pytest
# from fastapi.testclient import TestClient
# from pprint import pprint
# from main import app
#
# email = os.environ['TEST_EMAIL']
# password = os.environ['TEST_PASSWORD']
#
# login_successful_info = {"email": email, "password": password}
# login_fail_info = {
#     "email": email,
#     "password": str(random.choices(string.ascii_letters, k=10))}
#
# token = ''
#
# test_message = {
#     "user_id": "2158f687-e086-481a-9124-51d8be81ec87",
#     "title": "",
#     "content": "TEST TEST TEST",
#     "attachment_ids": [""],
#     "tags": ["howdy"],
#     "forum_id": "tank_building",
#     "recipient_id": None,
#     "channel_id": None,
#     "thread_id": "629689ee-3f1d-11ef-8b53-325096b39f47"}
#
#
# @pytest.fixture(scope="session")
# def client():
#     with TestClient(app) as client2:
#
#         # Set the cookies once and store them in the client instance
#         response = client2.post("/auth/login", params=login_successful_info)
#         pprint(response)
#         assert response.status_code == 200
#         assert client2.cookies.get('session')
#         assert len(client2.cookies.get('session')) > 0
#
#         return client2
#
# # def test_successful_login(client):
# #         # client(app, cookies={"session": response.cookies.get('session')})
# #
# #         # test successful logout
# #         # response = client.post("/auth/logout")
# #         # assert response.status_code == 200
# #
# #         # check that cookie is deleted
# #         # assert client.cookies.get('session') is None
#
#
# def test_create_message(client):
#     pprint(client.cookies.get('session'))
#     response = client.post("/message", json=test_message)
#     assert response.status_code == 200
#     created_msg_id = response.json()["results"]["id"]
#     test_message["id"] = created_msg_id
#
#
# def test_delete_message(client):
#     response = client.delete("/message",
#                              params={"message_id": test_message["id"]})
#     assert response.status_code == 200
#
#
# def test_successful_logout(client):
#     # test successful logout
#     response = client.post("/auth/logout")
#     assert response.status_code == 200
#
#     # check that cookie is deleted
#     assert client.cookies.get('session') is None
#
#
# def test_fail_login(client):
#     response = client.post("/auth/login", params=login_fail_info)
#     pprint(f"response: {str(response)}")
#     assert response.status_code == 401
