[flake8]
# E501 line too long (101 > 79 characters),
# E116 unexpected indentation (comment)
# E126 continuation line over-indented for hanging indent
# E401 multiple imports on one line
# E731 do not assign a lambda expression, use a def

ignore = E501,E116,E401
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    venv,
    .venv,
    */venv/*,
    */.venv/*

per-file-ignores =
    src/openai/assistant_nurse_chloe.py:W291
    src/ai_assistants/functions.py:E731
    src/auth/api3.py:E126

