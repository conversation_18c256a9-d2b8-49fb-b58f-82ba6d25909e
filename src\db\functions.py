from datetime import datetime, date
from fastapi.encoders import jsonable_encoder
from uuid import UUID


def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""

    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, UUID):
        # if the obj is uuid, we simply return the value of uuid
        return obj.hex
    else:
        # print('Trying to serialize this whacky thing: ' + str(obj))
        # print('Trying to serialize this whacky thing: ' + str(type(obj)))
        return jsonable_encoder(obj)
    raise TypeError("Type %s not serializable" % type(obj))
