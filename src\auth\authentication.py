import jwt
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from bcrypt import hashpw, gensalt, checkpw
from datetime import datetime, timedelta, UTC
import os
from dotenv import load_dotenv

load_dotenv()


# Secret key for session management


class AuthHandler:
    security = HTTPBearer()

    SECRET_KEY = os.getenv("AUTH_SECRET")

    def get_password_hash(self, password: str, salt: bytes = gensalt()) -> bytes:
        password_bytes = password.encode("utf-8")
        return hashpw(password_bytes, salt)

    def verify_password(self, password: str, hashed_password: bytes) -> bool:
        return checkpw(password.encode("utf-8"), hashed_password)

    def encode_token(self, user_id: str) -> str:
        payload = {
            "exp": datetime.now(UTC) + timedelta(minutes=60),
            "iat": datetime.now(UTC),
            "sub": user_id,
        }
        # return jwt.encode(payload, self.secret, algorithm='HS256').decode(
        # 'utf-8')
        return jwt.encode(payload, self.SECRET_KEY, algorithm="HS256")

    def decode_token(self, token: str) -> dict:
        try:
            payload = jwt.decode(token, self.SECRET_KEY, algorithms=["HS256"])
            return payload["sub"]
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except jwt.InvalidTokenError as e:
            raise HTTPException(status_code=401, detail="Invalid token: " + str(e))

    def auth_wrapper(self, auth: HTTPAuthorizationCredentials = Security(security)):
        return self.decode_token(auth.credentials)


if __name__ == "__main__":
    auth = AuthHandler()
    pword: str = "ftgwrwtb.16"
    password_bytes = pword.encode("utf-8")

    print(auth.get_password_hash(pword, bytes(gensalt())))
