<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Linter" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="hyperval-be-linter-image" />
        <option name="attachToContainerNeeded" value="true" />
        <option name="containerName" value="hyperval-be-linter-container" />
        <option name="publishAllPorts" value="true" />
        <option name="commandLineOptions" value="-it" />
        <option name="sourceFilePath" value="Dockerfile-linter" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>