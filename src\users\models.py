# from src.db.models import MongoBaseModel
from datetime import datetime
from pydantic import Field, EmailStr, UUID5, BaseModel
from src.users.levels.models import UserLevelBaseModel
from src.users.roles import UserRoleEnum
from src.charts.models import ColumnChartModel


# class UserBaseModel(MongoBaseModel):
class UserBaseModel(BaseModel):
    first_name: str = Field(...)
    last_name: str = Field(...)
    phone_number: str = Field(...)
    username: str = Field(...)
    email: str = EmailStr
    avatar_link: str | None = Field(...)


class UserCreateModel(UserBaseModel):
    password: str = Field(...)


class UserRecordModel(UserBaseModel):
    # first_name: str = Field(..., min_length=3)
    # year: int = Field(..., gt=1975, lt=2023)
    roles: list[UserRoleEnum] = Field(...)
    xp: int = Field(...)
    date_joined: int = Field(...)
    last_login: int = Field(...)
    tokens: int = Field(...)
    password_hash: bytes = Field(...)
    id: str = UUID5


class UserSessionModel(UserBaseModel):
    level: UserLevelBaseModel = Field(...)
    id: str = UUID5
    roles: list[UserRoleEnum] = Field(...)


# class UserActivityChartModel(BaseModel):
#     posts: int = Field(...)
#     replies: int = Field(...)
#     voting_power: float = Field(...)


class UserSimpleModel(BaseModel):
    username: str = Field(...)
    avatar_link: str | None = Field(...)
    level: UserLevelBaseModel = Field(...)
    id: str = Field(...)
    xp: int = Field(...)
    roles: list[UserRoleEnum] = Field(...)


class UserProfileModel(UserSimpleModel):
    date_joined: datetime = Field(...)
    profile_chart: ColumnChartModel = Field(...)
