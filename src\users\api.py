from fastapi import (
    APIRouter,
    Request,
    HTTPException,
    status,
    Depends,
    Response,
    Query,
    Form,
)
import boto3
from math import ceil
import os
from src.auth.api import get_current_user
from src.lib.constants import PAGE_SIZE, ONE_HOUR
from src.users.functions import (
    get_user_profile_record_from_user_record,
    get_user_simple_from_user_record,
)
from src.users.models import UserRecordModel, UserSessionModel

# UserProfileModel
from src.users.functions import create_full_user_record
from src.users.levels.functions import get_rank_from_xp
from typing import Annotated

# caching
from fastapi_cache.decorator import cache

# from src.charts.models import ColumnChartModel
# from typing import List, Annotated

users = APIRouter()


@users.post("", response_description="Create a new user")
async def create_user(
    request: Request,
    first_name: str = Form(...),
    last_name: str = Form(...),
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    phone_number: str = Form(...),
    avatar_link: str | None = Form(None),
):
    # Check if user already exists
    query = {"$or": [{"username": username}, {"email": email}]}
    count = await request.app.mongodb["users"].count_documents(query)
    if count > 0:
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Username or email already exists"
        )

    new_user_record = -1
    try:
        # Add debug logging
        user_data = {
            "first_name": first_name,
            "last_name": last_name,
            "username": username,
            "email": email,
            "password": password,
            "phone_number": phone_number,
            "avatar_link": avatar_link,
        }
        print(f"Creating user with data: {user_data}")

        # rank = await request.app.mongodb["users"].count_documents({})
        new_user_record = create_full_user_record(
            first_name=first_name,
            last_name=last_name,
            username=username,
            email=email,
            password=password,
            phone_number=phone_number,
            xp=0,
            avatar_link=avatar_link,
        )
        print(f"User record created: {new_user_record}")
        new_user_record = await request.app.mongodb["users"].insert_one(new_user_record)
    except Exception as e:
        print(f"Exception in create_user: {str(e)}")
        print(f"Exception type: {type(e)}")
        import traceback

        print(traceback.format_exc())
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return {"results": str(new_user_record.inserted_id)}


@users.patch("/update", response_description="Update user record")
async def update_user(
    request: Request,
    phone_number: str | None = None,
    avatar_link: str | None = None,
    username: str | None = None,
    email: str | None = None,
    user: UserRecordModel = Depends(get_current_user),
):
    result = None
    if phone_number:
        result = await request.app.mongodb["users"].update_one(
            {"id": user.id}, {"$set": {"phone_number": phone_number}}
        )
    if avatar_link:
        result = await request.app.mongodb["users"].update_one(
            {"id": user.id}, {"$set": {"avatar_link": avatar_link}}
        )
    if username:
        result = await request.app.mongodb["users"].update_one(
            {"id": user.id}, {"$set": {"username": username}}
        )
    if email:
        result = await request.app.mongodb["users"].update_one(
            {"id": user.id}, {"$set": {"email": email}}
        )

    if not result:
        raise HTTPException(
            status.HTTP_503_SERVICE_UNAVAILABLE, detail="User record update failed."
        )
    else:
        return Response("User updated successfully.", status_code=status.HTTP_200_OK)


@users.delete("/delete", response_description="Delete user record")
async def delete_user(
    request: Request,
    user_id: str | None = None,
    username: str | None = None,
    email: str | None = None,
    user: UserRecordModel = Depends(get_current_user),
):
    result = None
    if user_id:
        if user.id != user_id:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You cannot delete another user's " "account.",
            )
        result = await request.app.mongodb["users"].delete_one({"id": user_id})
    if username:
        if user.username != username:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You cannot delete another user's " "account.",
            )
        result = await request.app.mongodb["users"].delete_one({"username": username})
    if email:
        if user.email != email:
            raise HTTPException(
                status.HTTP_403_FORBIDDEN,
                detail="You cannot delete another user's " "account.",
            )
        result = await request.app.mongodb["users"].delete_one({"email": email})

    if not result or result.deleted_count == 0:
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="User not found!  No record was deleted."
        )
    else:
        return {"results": str(result.deleted_count)}


@users.get("", response_description="Find user by username.")
async def get_user(
    request: Request, username: str | None = None, user_id: str | None = None
):
    user = {}
    if username:
        user = await request.app.mongodb["users"].find_one({"username": username})
    if user_id:
        user = await request.app.mongodb["users"].find_one({"id": user_id})
    if not user:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found!")

    user = UserRecordModel(**user)
    return {"username": username, "result": user}


@users.get("s", response_description="List all users")
@cache(expire=ONE_HOUR)
async def list_all_users(
    request: Request,
    # min_price: int = 0,
    user_id_list: Annotated[list[str] | None, Query()] = None,
    page: int = 1,
    page_size: int = PAGE_SIZE,
):
    skip = (page - 1) * page_size

    # query = {"price": {"$lt": max_price, "$gt": min_price}}
    query = {}

    if user_id_list and len(user_id_list) > 0:
        query = {"id": {"$in": user_id_list}}

    # results = mycollection.find(query)
    #
    # for document in results:
    #     print(document)
    # if brand:
    #     query["brand"] = brand

    # count total docs

    total = await request.app.mongodb["users"].count_documents(query)
    pages = ceil(total / page_size)
    full_query = (
        request.app.mongodb["users"]
        .find(query)
        .sort("username")
        .skip(skip)
        .limit(page_size)
    )

    results = []
    async for raw_user in full_query:
        try:
            # Handle password_hash more safely
            user_data = {**raw_user}

            # Ensure password_hash is bytes
            if "password_hash" in user_data:
                if isinstance(user_data["password_hash"], str):
                    user_data["password_hash"] = user_data["password_hash"].encode()
                elif not isinstance(user_data["password_hash"], bytes):
                    # If it's neither string nor bytes, convert to string then bytes
                    user_data["password_hash"] = str(
                        user_data["password_hash"]
                    ).encode()

            # Create user model and get simple version
            user_model = UserRecordModel(**user_data)
            simple_user = get_user_simple_from_user_record(user_model)
            results.append(simple_user)
        except Exception as e:
            print(
                f"Error processing user {raw_user.get('username', 'unknown')}: {str(e)}"
            )
            # Skip this user but continue processing others
            continue

    return {"results": results, "pages": pages, "total": total}


# Protected endpoint
@users.get("/session", response_description="Get user session data")
# @role_required([UserRoleEnum.ADMIN])
async def get_user_session(user: UserRecordModel = Depends(get_current_user)):
    user_session = UserSessionModel(
        first_name=user.first_name,
        last_name=user.last_name,
        username=user.username,
        email=user.email,
        phone_number=user.phone_number,
        avatar_link=user.avatar_link,
        roles=user.roles,
        id=user.id,
        level=get_rank_from_xp(user.xp),
    )
    return {
        "message": f"Hello {user.username}, you have access to this "
        f"protected endpoint!",
        "result": user_session,
    }


# TODO: need to get real data to the chart
# Protected endpoint
@users.get("/profile", response_description="Get user profile data.")
# @role_required([UserRoleEnum.ADMIN])
async def get_user_profile(request: Request, user_id: str):
    user = {}
    if user_id:
        user = await request.app.mongodb["users"].find_one({"id": user_id})

    if not user:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found!")
    user = UserRecordModel(**user)

    # class UserProfileModel(BaseModel):
    #     username: str = Field(...)
    #     avatar_link: str | None = Field(...)
    #     level: UserLevelBaseModel = Field(...)
    #     xp: int = Field(...)
    #     roles: list[UserRoleEnum] = Field(...)
    #     date_joined: datetime = Field(...)
    #     profile_chart: UserActivityChartModel = Field(...)
    user_profile = get_user_profile_record_from_user_record(user)

    return {"result": user_profile}


@users.get("/images", response_description="List all images of a user.")
async def get_all_user_images(user_id: str):
    user_dir = f"users/{user_id}/img/"
    try:
        response = s3_client.list_objects_v2(Bucket=AWS_S3_BUCKET_NAME, Prefix=user_dir)
        files = response.get("Contents")
        filename_test = []
        for file in files:
            filename_test.append(file["Key"])

        # Extract the list of image URLs
        image_urls = []
        for obj in response.get("Contents", []):
            image_urls.append(
                {
                    "url": f"https://{AWS_S3_BUCKET_NAME}.s3."
                    f"{AWS_S3_REGION}.amazonaws.com/" + obj["Key"],
                    # 'url': s3_client.generate_presigned_url("get_object",
                    #                                         Params={"Bucket":
                    #                                         AWS_S3_BUCKET_NAME, "Key": obj["Key"]},
                    #                                         ExpiresIn=3600
                    # URL expiration time (in seconds)
                    # ),
                    "filename": obj["Key"].split("/")[-1],
                }
            )
        return {"image_urls": image_urls, "filename_test": filename_test}
    except Exception as e:  # noqa
        raise HTTPException(status_code=500, detail="Failed to fetch images")
