from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, UploadFile
from fastapi.responses import JSONResponse
from botocore.exceptions import NoCredentialsError, ClientError
from src.globals import AWS_S3_BUCKET_NAME, AWS_S3_REGION
from src.users.models import UserRecordModel


def get_user_img_dir(user: UserRecordModel):
    return f"{user.id}/img/"


def upload_image(s3_client, user: UserRecordModel, file: UploadFile):
    if not file:
        raise HTTPException(status_code=400, detail="No file provided.")
    try:
        # Generate a unique file name using the original filename
        file_name = "users/" + get_user_img_dir(user) + (file.filename or "unnamed_file")

        # Upload the file to S3
        s3_client.upload_fileobj(
            file.file,
            # File object
            AWS_S3_BUCKET_NAME,
            # Bucket name
            file_name,
            # File name in S3
            ExtraArgs={"ACL": "public-read"},
            # Make file public
        )

        # Get the URL of the uploaded file
        file_url = (
            f"https://{AWS_S3_BUCKET_NAME}.s3."
            f"{AWS_S3_REGION}.amazonaws.com/{file_name}"
        )

        return JSONResponse(status_code=200, content={"file_url": file_url})
    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")
    except ClientError as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload image: {str(e)}.")
