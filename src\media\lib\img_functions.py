from fastapi import HTTPEx<PERSON>, UploadFile
from fastapi.responses import J<PERSON><PERSON>esponse
from botocore.exceptions import NoCredentialsError, ClientError
from src.globals import AWS_S3_BUCKET_NAME, AWS_S3_REGION
from src.users.models import UserRecordModel
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse


def get_user_img_dir(user: UserRecordModel):
    return f"{user.id}/img/"


def upload_image_to_s3(s3_client, user: UserRecordModel, file: UploadFile):
    try:
        # Upload the file to S3
        s3_client.upload_fileobj(
            file.file,
            # File object
            AWS_S3_BUCKET_NAME,
            # Bucket name
            file_name,
            # File name in S3
            ExtraArgs={"ACL": "public-read"},
            # Make file public
        )

        # Get the URL of the uploaded file
        file_url = (
            f"https://{AWS_S3_BUCKET_NAME}.s3."
            f"{AWS_S3_REGION}.amazonaws.com/{file_name}"
        )

        return JSONResponse(status_code=200, content={"file_url": file_url})
    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")
    except ClientError as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload image: {str(e)}.")


def get_image_from_key(s3_client, image_key: str):
    try:
        # Get the image object from S3
        response = s3_client.get_object(
            Bucket=AWS_S3_BUCKET_NAME,
            Key=image_key
        )

        # Determine content type based on file extension
        content_type = "image/jpeg"  # default
        if image_key.lower().endswith(('.png', '.PNG')):
            content_type = "image/png"
        elif image_key.lower().endswith(('.gif', '.GIF')):
            content_type = "image/gif"
        elif image_key.lower().endswith(('.webp', '.WEBP')):
            content_type = "image/webp"

        # Return the image as a streaming response
        return StreamingResponse(
            response['Body'],
            media_type=content_type,
            headers={
                "Content-Disposition": f"inline; filename={image_key.split('/')[-1]}",
                "Cache-Control": "public, max-age=3600"
            }
        )

    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="Image not found.")
        else:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve image: {str(e)}.")
    except NoCredentialsError:
        raise HTTPException(status_code=403, detail="Credentials not available.")


def edit_image(s3_client, file:UploadFile, image_key):
    s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, image_key)
    # return {"message": "Image edited successfully"}