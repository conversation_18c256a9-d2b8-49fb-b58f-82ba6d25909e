from enum import Enum


class UserRoleEnum(Enum):
    FREE = "FREE"
    PAID = "PAID"
    MODERATOR = "MODERATOR"
    ADMIN = "ADMIN"
    CONTENT_CREATOR = "CONTENT_CREATOR"
    GUEST = "GUEST"
    BANNED = "BANNED"

    def describe(self):
        # Providing a human-readable description for each role
        descriptions = {
            UserRoleEnum.FREE: "Free tier user with basic access",
            UserRoleEnum.PAID: "Paid tier user with premium access",
            UserRoleEnum.MODERATOR: "User with moderation privileges",
            UserRoleEnum.ADMIN: "Administrator with full access",
            UserRoleEnum.CONTENT_CREATOR: "User who creates content",
            UserRoleEnum.GUEST: "Guest user with limited access",
            UserRoleEnum.BANNED: "User who is banned from the platform",
        }
        return descriptions.get(self, "Unknown role")


# # Example usage:
# if __name__ == "__main__":
#     for role in UserRoleEnum:
#         print(f"{role.name}: {role.describe()}")
