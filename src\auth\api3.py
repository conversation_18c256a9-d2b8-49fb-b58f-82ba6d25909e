# from fastapi import APIRouter, Request, HTTPException, status

from src.auth.authentication import <PERSON><PERSON><PERSON><PERSON><PERSON>
# from src.users.models import UserRecordModel
from fastapi import APIRouter, Request, Depends, HTTPException, Response
from fastapi.responses import RedirectResponse

# This must be randomly generated
RANDON_SESSION_ID = "iskksioskassyidd"

# This must be a lookup on user database
USER_CORRECT = ("admin", "admin")

# This must be <PERSON>is, Memcached, SQLite, KV, etc...
SESSION_DB = {}

auth_router = APIRouter()
auth_handler = AuthHandler()


@auth_router.post("/login")
async def session_login(request: Request, username: str, password: str):
    allow = (username, password) == USER_CORRECT
    if allow is False:
        raise HTTPException(status_code=401)
    response = RedirectResponse("/", status_code=302)
    response.set_cookie(key="Authorization", value=RANDON_SESSION_ID)
    SESSION_DB[RANDON_SESSION_ID] = username
    return response


@auth_router.post("/logout")
async def session_logout(response: Response):
    response.delete_cookie(key="Authorization")
    SESSION_DB.pop(RANDON_SESSION_ID, None)
    return {"status": "logged out"}


def get_auth_user(request: Request):
    """verify that user has a valid session"""
    session_id = request.cookies.get("Authorization")
    if not session_id:
        raise HTTPException(status_code=401)
    if session_id not in SESSION_DB:
        raise HTTPException(status_code=403)
    return True


@auth_router.get("/", dependencies=[Depends(get_auth_user)])
async def secret():
    return {"secret": "info"}


# @auth_router.post('/login', response_description="Login.")
# async def login(request: Request, username: str, password: str):
#     query = {"username": username}
#     user = request.app.mongodb["users"].find(query)
#     count = await request.app.mongodb["users"].count_documents(query)
#     print("count: " + str(count))
#     if not user or count == 0:
#         raise HTTPException(status.HTTP_404_NOT_FOUND,
#                             detail="User not found!")
#     results = [UserRecordModel(**u) async for u in user]
#
#     user = results[0]
#     # if checkpw(password.encode('utf-8'), user.password_hash):
#     if auth_handler.verify_password(password, user.password_hash):
#         token = auth_handler.encode_token(user.id)
#         response = {
#             "content": {
#                 "token": token,
#                 "msg": f"Login successful.  Welcome back {username}."}}
#         return response
#     else:
#         raise HTTPException(status.HTTP_401_UNAUTHORIZED,
#                             detail="Incorrect username or password.")
