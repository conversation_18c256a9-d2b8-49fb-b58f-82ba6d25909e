from fastapi import <PERSON>Router, Request, HTTPException, status

from src.auth.authentication import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.users.models import UserRecordModel

auth_router = APIRouter()
auth_handler = AuthHandler()


@auth_router.post("/login", response_description="Login.")
async def login(request: Request, email: str, password: str):
    query = {"email": email}
    user = request.app.mongodb["users"].find(query)
    count = await request.app.mongodb["users"].count_documents(query)
    # print("count: " + str(count))
    if not user or count == 0:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found!")
    results = [UserRecordModel(**u) async for u in user]

    user = results[0]
    # if checkpw(password.encode('utf-8'), user.password_hash):
    if auth_handler.verify_password(password, user.password_hash):
        token = auth_handler.encode_token(user.id)
        response = {
            "content": {
                "token": token,
                "msg": f"Login successful.  Welcome back {user.username}.",
            }
        }
        return response
    else:
        raise HTTPException(
            status.HTTP_401_UNAUTHORIZED, detail="Incorrect username or password."
        )
