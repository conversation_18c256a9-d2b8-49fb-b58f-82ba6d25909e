import os
from dotenv import load_dotenv
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi

load_dotenv()


def get_db_url():
    if "DB_URL" in os.environ:
        db_url = os.environ["DB_URL"]
        return db_url

    db_user = os.environ["DB_USERNAME"]
    db_pswrd = os.environ["DB_PASSWORD"]
    db_uri = os.environ["DB_URI"]
    db_uri = db_uri.replace("$(DB_USERNAME)", db_user)
    db_uri = db_uri.replace("$(DB_PASSWORD)", db_pswrd)

    return db_uri


def get_db_client():
    db_url = get_db_url()
    client = MongoClient(db_url, server_api=ServerApi("1"))

    return client
