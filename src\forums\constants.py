from typing import List
from src.forums.models import ForumEntryModel
from pprint import pprint

FORUMS_LIST: List[ForumEntryModel.schema_json] = [{
    "label": "Beginner Guides",
    "description": "New to marine aquaculture?  Check this out!",
    "icon": "fa-user-magnifying-glass",
    "id": "beginner_guides",
    "subtopics": []}, {
    "label": "Fish Care",
    "description": "",
    "icon": "fa-fish",
    "id": "fish_care",
    "subtopics": []}, {
    "label": "Coral Care",
    "description": "",
    "icon": "fa-c",
    "id": "coral_care",
    "subtopics": []}, {
    "label": "Invertebrate Care",
    "description": "",
    "icon": "fa-lobster",
    "id": "invertebrate_care",
    "subtopics": [{
        "label": "Anemones",
        "description": "Floats like a butterfly and stings like a bee.",
        "icon": "",
        "id": "anemones",
        "subtopics": []}, {
        "label": "Starfish",
        "description": "",
        "icon": "fa-star-sharp",
        "id": "starfish",
        "subtopics": []}, {
        "label": "Snails & Conches",
        "description": "",
        "icon": "snails_conches",
        "id": "",
        "subtopics": []}, {
        "label": "Crabs & Lobster",
        "description": "",
        "icon": "",
        "id": "crab_lobster",
        "subtopics": []}, {
        "label": "Cucumbers & Slugs",
        "description": "",
        "icon": "",
        "id": "cucumbers_slugs",
        "subtopics": []}, {
        "label": "Cephalopod",
        "description": "Octopus, squid, and cuttlefish, oh my!",
        "icon": "fa-brands fa-octopus-deploy",
        "id": "cephalopod",
        "subtopics": []}, ]}, {
    "label": "Pests",
    "description": "Worms, parasites, bacteria, etc.",
    "icon": "fa-bacteria",
    "id": "pests",
    "subtopics": []}, {
    "label": "Water Chemistry & Dosing",
    "description": "",
    "icon": "fa-flask-gear",
    "id": "water_chemistry",
    "subtopics": []}, {
    "label": "Emergency Help",
    "description": "\"We need AmmoLock. Lots of AmmoLock.\"",
    "icon": "fa-triangle-exclamation",
    "id": "emergency_help",
    "subtopics": []}, {
    "label": "Equipment",
    "description": "Plumbing, lighting, filtration, UV, ozone, powerheads, "
                   "etc",
    "icon": "fa-tools",
    "id": "equipment",
    "subtopics": []}, {
    "label": "Tank Building",
    "description": "",
    "icon": "fa-wrench",
    "id": "tank_building",
    "subtopics": []}, {
    "label": "DIY Projects",
    "description": "",
    "icon": "fa-user-helmet-safety",
    "id": "diy_projects",
    "subtopics": []}, {
    "label": "Photography",
    "description": "",
    "icon": "fa-camera",
    "id": "photography",
    "subtopics": []}, {
    "label": "Marketplace",
    "description": "",
    "icon": "fa-messages-dollar",
    "id": "marketplace",
    "subtopics": []}, {
    "label": "Events and Meetups",
    "description": "",
    "icon": "fa-calendar-star",
    "id": "events_meetups",
    "subtopics": []}, {
    "label": "Random",
    "description": "Everything which does not fit anywhere else.",
    "icon": "fa-block-question",
    "id": "random",
    "subtopics": []}]
    # , {
    # "label": "None",
    # "description": "",
    # "icon": "",
    # "id": "",
    # "subtopics": []}, ]

FORUMS_DICT = {entry["id"]: entry for entry in FORUMS_LIST}


if __name__ == "__main__":
    print("Forums dict:")
    pprint(FORUMS_DICT)


# class ForumCategory(Enum):
#     GENERAL_DISCUSSION = "General Discussion"
#     BEGINNER_GUIDES = ""
#     TANK_BUILDING = "Tank Building"
#     FISH_CARE = "Fish Care"
#     CORAL_CARE = "Coral Care"
#     INVERTEBRATE_CARE = "Invertebrate Care"
#     EQUIPMENT = "Equipment"
#     WATER_CHEMISTRY = "Water Chemistry"
#     DIY_PROJECTS = "DIY Projects"
#     MARKETPLACE = "Marketplace"
#     PHOTOGRAPHY = "Photography"
#     EMERGENCY_HELP = "Emergency Help"
#     EVENTS_AND_MEETUPS = "Events and Meetups"
#     NONE = 'NONE'
#
#     @staticmethod
#     def print_categories(self):
#         for category in ForumCategory:
#             print(category.name, ":", category.value)
#
#     @staticmethod
#     def get_list_of_names() -> List[str]:
#         return [x.value for x in ForumCategory]
