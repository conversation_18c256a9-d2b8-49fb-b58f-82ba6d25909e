from fastapi import HTTPException, status, Request, Response, Form
from fastapi.routing import APIRouter

# from fastapi.security import HTTPBasicCredentials
from src.auth.authentication import AuthHandler
from src.users.models import UserRecordModel
from src.users.roles import UserR<PERSON>Enum
from itsdangerous import URLSafeTimedSerializer, BadSignature
from typing import Callable, Annotated
import os
from dotenv import load_dotenv

load_dotenv()

# Secret key for session management
SECRET_KEY = os.getenv("AUTH_SECRET")
COOKIE_NAME = "session"
COOKIE_MAX_AGE = 3600 * 24  # 1 day

# Serializer for secure session cookie
serializer = URLSafeTimedSerializer(SECRET_KEY)

auth_router = APIRouter()


# Login endpoint
@auth_router.post("/login")
async def login(
    request: Request,
    email: Annotated[str, Form()],
    password: Annotated[str, Form()],
    response: Response,
):
    # async def login(credentials: HTTPBasicCredentials, response: Response):
    query = {"email": email}
    user = request.app.mongodb["users"].find(query)
    count = await request.app.mongodb["users"].count_documents(query)
    if not user or count == 0:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found!")
    results = [UserRecordModel(**u) async for u in user]

    user = results[0]

    auth_handler = AuthHandler()
    if auth_handler.verify_password(password, user.password_hash):
        # Create a session token
        token = serializer.dumps(user.username)
        response.set_cookie(
            COOKIE_NAME, token, max_age=COOKIE_MAX_AGE, httponly=True, secure=True
        )
        return {"message": f"Login successful.  Welcome back {user.username}!"}
    else:
        raise HTTPException(
            status.HTTP_401_UNAUTHORIZED, detail="Incorrect username or password."
        )


# Logout endpoint
@auth_router.post("/logout")
async def logout(response: Response):
    response.delete_cookie(COOKIE_NAME)
    return {"message": "Logout successful"}


# Get current user from cookie
async def get_current_user(request: Request) -> UserRecordModel:
    # print("GET_CURRENT_USER")
    token = request.cookies.get(COOKIE_NAME)
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated"
        )
    # print("got token, trying to load username from it")
    try:
        username = serializer.loads(token, max_age=COOKIE_MAX_AGE)
    except BadSignature:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid or expired token"
        )

    user = request.app.mongodb["users"].find({"username": username})
    results = [UserRecordModel(**u) async for u in user]
    user = results[0]
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
        )

    return user


# Authorization decorator
def role_required(required_roles: list[UserRoleEnum]) -> Callable:
    # print("0" * 80)
    # pprint(required_roles)
    # print(type(required_roles[0]))

    def decorator(func: Callable):
        # print("/" * 80)
        # print("callable: ")
        # print(func)

        async def wrapper(*args, **kwargs):
            # print("9" * 80)
            request: Request = kwargs.get("request")
            # print("8" * 80)
            user = get_current_user(request)
            # print("#" * 80)
            # print(user.role)
            # print("#" * 80)
            if not any(role in user.roles for role in required_roles):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions",
                )
            return await func(*args, **kwargs, user=user)

        return wrapper

    return decorator


# # Protected endpoint
# @auth_router.get('/session')
# # @role_required([UserRoleEnum.ADMIN])
# async def protected(user: UserRecordModel = Depends(get_current_user)):
#     print('HERE HERE HERE HERE HERE\n\n\n')
#     return {"message": f"Hello {user.username}, you have access to this "
#                        f"protected endpoint!"}

# Start the application
# if __name__ == '__main__':
#     import uvicorn
#
#     uvicorn.run(app, host='0.0.0.0', port=8000)
