from src.ai_assistants.models import AiAssistantModel
from dotenv import load_dotenv
import os

load_dotenv()

CHLOE = AiAssistantModel(name="<PERSON> <PERSON>",
                         description="Cephalopod Expert",
                         id=os.environ['OPENAI_CHLOE_ASSISTANT_ID'],
                         store_id=os.environ['OPENAI_CHLOE_STORE_ID'],
                         image_url='')

SOLANGE = AiAssistantModel(name="<PERSON><PERSON><PERSON>",
                           description="Coral Expert",
                           id='',
                           store_id='',
                           image_url='')
KATIE = AiAssistantModel(name="<PERSON>",
                         description="Echinoderm Expert",
                         id=os.environ['OPENAI_KATIE_ASSISTANT_ID'],
                         store_id=os.environ['OPENAI_KATIE_STORE_ID'],
                         image_url='')
