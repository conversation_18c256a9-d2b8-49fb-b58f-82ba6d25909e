from fastapi import APIRouter
from src.db.db_wrapper import get_db_client

db_router = APIRouter()


@db_router.get("/boop")
async def db_boop():
    return {"response": "boop!"}


@db_router.get("/ping")
async def db_test():
    client = get_db_client()
    msg = ""
    try:
        client.admin.command("ping")
        msg = "Pinged your deployment. You successfully connected to MongoDB!"
    except Exception as e:
        msg = e
        # print(e)
    return {"response": msg}


@db_router.get("/list-dbs")
async def list_dbs():
    client = get_db_client()
    msg = ""
    try:
        # List all databases
        # databases = client.list_database_names()
        collections = client.list_collection_names()

        # Print each database name
        # for db_name in databases:
        #     msg += msg + ', ' + db_name
        # Print each database name
        for cname in collections:
            msg = msg + ", " + cname + "\n"  # msg = (msg + 'database  #  #
            # names: ' + str(client.list_database_namess())  #        +  #
            # '\n')  # msg = msg + 'collection names: ' + str(  #  #
            # client.list_collection_names()) + '\n'
    except Exception as e:
        msg = "\n Something bad happened. \n" + str(e)
        # print(e)
    return {"response": msg}


# def list_everything():
#     client = get_db_client()
#     msg = ""
#     try:
#         client.admin.command("ping")
#         print("Pinged your deployment. You successfully connected to MongoDB!")
#         # List all databases
#         databases = client.list_database_names()
#         print("databases:")
#         for dbname in databases:
#             print("\t" + dbname)
#         reefDojoDb = client.get_database(name="reefdojo")

#         collections = reefDojoDb.list_collection_names()
#         print("collections:")
#         for cname in collections:
#             print("\t" + cname)

#         usersCollection = reefDojoDb.get_collection(name="users")
#         something = usersCollection.find({"first_name": "Jane"})
#         print("yo yo yo: " + str(something[0]))
#         print()
#         print("something: " + str(something))
#         for s in something:
#             print("\t" + str(s))
#     except Exception as e:
#         msg = "\n Something bad happened. \n" + str(e)
#         print(e)
#     print(msg)


# if __name__ == "__main__":
#     list_everything()
