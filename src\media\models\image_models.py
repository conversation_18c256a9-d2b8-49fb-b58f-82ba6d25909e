from datetime import datetime
from pydantic import Field, UUID5, BaseModel
from src.reactions.models import ReactionEnum


class ImageBaseModel(BaseModel):
    name: str = Field(...)
    description: str = Field(...)
    date_created: datetime = Field(...)
    id: str = UUID5
    url: str = Field(...)

    # hash is used to find duplicate images
    hash: str
    reactions: dict[ReactionEnum, int] = Field(default_factory=dict)
    user_id: str = UUID5
    # posts, user_id (profile pic), aquarium albums, etc
    object_ids: list[str] = Field(default_factory=list)
