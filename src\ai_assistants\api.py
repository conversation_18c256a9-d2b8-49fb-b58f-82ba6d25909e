from fastapi import APIRouter, Request, Depends, HTTPException, status
from src.auth.api import get_current_user
from src.users.models import UserRecordModel
from src.ai_assistants.functions import ask_chloe, ask_katie

ai_assistants = APIRouter()


@ai_assistants.post("/chloe", response_description="Ask Chloe")
async def get_answer_from_chloe(request: Request, message: str,
                                user: UserRecordModel = Depends(get_current_user)):
    if user.tokens <= 0:
        raise HTTPException(status.HTTP_400_BAD_REQUEST,
                            detail="Insufficient tokens for AI assistant request.")
    run = ''
    try:
        run = ask_chloe(message)
    except Exception as e:
        return {"results": str(e)}
    user.tokens = user.tokens - 1
    await (request.app.mongodb["users"].update_one({"id": user.id}, {"$set": {"tokens": user.tokens}}))
    # .find_one({# noqa        "username": user_data.username}))
    return {"results": run}


@ai_assistants.post("/katie", response_description="Ask Katie")
async def get_answer_from_katie(request: Request, message: str,
                                user: UserRecordModel = Depends(get_current_user)):
    if user.tokens <= 0:
        raise HTTPException(status.HTTP_400_BAD_REQUEST,
                            detail="Insufficient tokens for AI assistant request.")
    run = ''
    try:
        run = ask_katie(message)
    except Exception as e:
        return {"results": str(e)}
    user.tokens = user.tokens - 1
    await (request.app.mongodb["users"].update_one({"id": user.id}, {"$set": {"tokens": user.tokens}}))
    return {"results": run}
