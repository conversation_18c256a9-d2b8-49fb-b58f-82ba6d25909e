from fastapi.testclient import Test<PERSON>lient
from main import app
import random
import string

test_user = {
    "first_name": "<PERSON>rk",
    "last_name": "<PERSON><PERSON>",
    "username": str(random.choices(string.ascii_letters, k=10)),
    "email": str(random.choices(string.ascii_letters, k=10)) + "@" + str(
        random.choices(string.ascii_letters, k=10)) + ".com",
    "password": "dorkbutt",
    "avatar_link": None,
    "phone_number": "5557778888", }


def test_create_user():
    with TestClient(app) as client:
        response = client.post("/user", json=test_user)
        assert response.status_code == 200


def test_get_user():
    with TestClient(app) as client:
        response = client.get("/user",
                              params={"username": test_user["username"]})
        assert response.status_code == 200
        assert response.json()["results"][0]["first_name"] == "Dork"


def test_delete_user():
    with TestClient(app) as client:
        response = client.post("/user/delete",
                               params={"username": test_user["username"]})
        assert response.status_code == 200


def test_get_users():
    with TestClient(app) as client:
        response = client.get("/users")
        assert response.status_code == 200  # {  #  # "id": "foo",
        #     "title": "Foo",  #     "description": "There goes  # my hero",
        # }
