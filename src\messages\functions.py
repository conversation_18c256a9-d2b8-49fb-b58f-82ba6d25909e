from src.messages.models import Message<PERSON>reateModel, MessageRecordModel
from uuid import uuid4
import datetime
from src.forums.constants import FORUMS_DICT

FEED_MSG_TRUNCATE_AMT = 45


def create_full_message_record(init_message: MessageCreateModel) -> MessageRecordModel:
    today = datetime.datetime.now(tz=datetime.timezone.utc).isoformat()

    msg = MessageRecordModel(
        title=init_message.title,
        content=init_message.content,
        user_id=init_message.user_id,
        recipient_id=init_message.recipient_id,
        tags=init_message.tags,
        replies=[],
        id=str(uuid4()),
        date_created=today,
        last_modified=today,
    ).model_dump()
    # print("Message created:")
    # pprint(msg)
    # print("#" * 80)
    return msg


# Used for formatting the MongoDB response for the group query
# handles the most recent messages for a single forum subject
def format_feed_group_message(mongoDbGroupEntry):
    messages = []
    # for m in mongoDbGroupEntry["messages"]:
    #     del m['_id']
    #     messages.append(MessageRecordModel(**m))
    for m in mongoDbGroupEntry["messages"]:
        del m["_id"]
        title = m["title"]
        title = title if title and len(title) > 1 else "Replying to *****"
        m["title"] = title
        content = m["content"]
        content = (
            content[:FEED_MSG_TRUNCATE_AMT] + "..."
            if len(content) > FEED_MSG_TRUNCATE_AMT
            else content
        )
        m["content"] = content
        # m['id'] = str(m['id'].decode('utf-8'))
        messages.append(m)

    forum_entry = FORUMS_DICT[mongoDbGroupEntry["_id"]]
    retval = {
        "forum_id": mongoDbGroupEntry["_id"],
        "forum_label": forum_entry["label"],
        "forum_description": forum_entry["description"],
        "forum_icon": forum_entry["icon"],
        "messages": messages,
    }
    return retval
