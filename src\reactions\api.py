from fastapi import <PERSON>Rout<PERSON>, Request, HTTPException, status, Depends
from src.auth.api import get_current_user
from src.users.models import UserRecordModel
from src.reactions.functions import get_reaction_target
from src.reactions.models import ReactionEnum
from uuid import UUID as UUID5


reactions = APIRouter()


@reactions.post("", response_description="Create a new message")
async def create_reaction(
    request: Request,
    target_id: UUID5,
    reaction: ReactionEnum,
    user: UserRecordModel = Depends(get_current_user),
):
    user_count = await request.app.mongodb["users"].count_documents({"id": user.id})
    # user not found check
    if user_count < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="User not found.",
        )

    target_object = await get_reaction_target(target_id)
    reaction_record = -1
    if target_object:
        try:
            reaction_record = await request.app.mongodb["messages"].insert_one(  # noqa
                dict(
                    {
                        "user_id": user.id,
                        "target_id": target_id,
                        "reaction": reaction,
                    }
                )
            )
        except Exception as e:
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return {"results": reaction_record}


@reactions.delete("", response_description="Delete a reaction")
async def delete_reaction(
    request: Request,
    target_id: UUID5,
    user: UserRecordModel = Depends(get_current_user),
):
    # Find the reaction with the given target_id and user_id
    reaction = await request.app.mongodb["reactions"].find_one(
        {"target_id": str(target_id), "user_id": user.id}
    )

    # If no reaction found, return 404
    if not reaction:
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Reaction not found.",
        )

    # Delete the reaction
    try:
        result = await request.app.mongodb["reactions"].delete_one(
            {"target_id": str(target_id), "user_id": user.id}
        )
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

    if result.deleted_count == 0:
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No reaction was deleted. Internal error.",
        )

    return {"results": {"status": result.acknowledged}}
