from fastapi import APIRouter, Request
from math import ceil

from src.messages.models import MessageRecordModel

threads = APIRouter()


@threads.get("s", response_description="List all threads")
async def list_all_threads(request: Request,
                           page: int = 1,
                           page_size: int = 10):
    skip = (page - 1) * page_size

    query = {"$expr": {"$eq": ["$id", "$thread_id"]}}
    # query2 = {
    #     "$redact": {
    #         "$cond": [{"$eq": ["$start", "$end"]}, "$$KEEP", "$$PRUNE"]
    #     }
    # }
    # if brand:
    #     query["brand"] = brand

    # count total docs
    total = await request.app.mongodb["messages"].count_documents(query)
    pages = ceil(total / page_size)

    full_query = (
        request.app.mongodb["messages"].find(query).sort("date_created").skip(
            skip).limit(page_size))
    # full_query = (
    #     request.app.mongodb["messages"].aggregate([query2]).sort(
    #     "date_created").skip(
    #         skip).limit(page_size))

    results = [MessageRecordModel(**raw_msg) async for raw_msg in full_query]

    return {"results": results, "pages": pages, "total": total}


@threads.get("/{id}", response_description="Get messages for a thread.")
async def list_thread_messages(request: Request,
                               id: str,
                               page: int = 1,
                               page_size: int = 10):
    skip = (page - 1) * page_size

    query = {"thread_id": id}
    # query2 = {
    #     "$redact": {
    #         "$cond": [{"$eq": ["$start", "$end"]}, "$$KEEP", "$$PRUNE"]
    #     }
    # }
    # if brand:
    #     query["brand"] = brand

    # count total docs
    total = await request.app.mongodb["messages"].count_documents(query)
    pages = ceil(total / page_size)

    full_query = (
        request.app.mongodb["messages"].find(query).sort("date_created").skip(
            skip).limit(page_size))
    # full_query = (
    #     request.app.mongodb["messages"].aggregate([query2]).sort(
    #     "date_created").skip(
    #         skip).limit(page_size))

    results = [MessageRecordModel(**raw_msg) async for raw_msg in full_query]
    # results = [{**raw_msg} async for raw_msg in full_query]

    return {"results": results, "pages": pages, "total": total}

# @messages.post("", response_description="Create a new message")
# async def create_message(request: Request, message: MessageCreateModel):
#     user_count = await (
#         request.app.mongodb["users"].count_documents({"id":
#         message.user_id}))
#     # user not found check
#     if user_count < 1:
#         raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY,
#                             detail="User not found. "
#                                    "  Unable to create message.")
#     # empty message check
#     if len(message.content) < 1:
#         raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY,
#                             detail="Empty message content;"
#                                    " unable to create message.")
#     new_msg_record = -1
#     try:
#         full_message = create_full_message_record(message)
#         new_msg_record = await ( # noqa
#             request.app.mongodb["messages"].insert_one(dict(full_message)))
#     except Exception as e:
#         raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR,
#                             detail=str(e))
#     retval = full_message["id"]
#     return {"results": {"id": retval}}
#
#
# # TODO: NEED TO ENFORCE AUTHORIZATION ON THIS ONE
# @messages.delete("", response_description="Delete a message")
# async def delete_message(request: Request, message_id: str):
#     message_count = await (
#         request.app.mongodb["messages"].count_documents({"id": message_id}))
#     # user not found check
#     if message_count < 1:
#         raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY,
#                             detail="Message not found. "
#                                    "  Unable to delete message.")
#     try:
#         result = await (
#             request.app.mongodb["messages"].delete_one({"id": message_id}))
#     except Exception as e:
#         raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR,
#                             detail=str(e))
#     if result.deleted_count == 0:
#         raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR,
#                             detail="No message was deleted.  Internal
#                             error.")
#     return {"results": {"status": result.acknowledged}}
