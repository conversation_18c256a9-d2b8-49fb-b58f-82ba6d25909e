from uuid import UUID
from typing import Optional
import logging


async def get_reaction_target(request, target_id: UUID) -> Optional[dict]:
    """
    Search for a message where id matches the target_id.

    Args:
        request: The FastAPI request object with MongoDB connection
        target_id: UUID of the target message

    Returns:
        The matching message or None if not found
    """
    try:
        # Find messages where id matches target_id
        messages = (
            await request.app.mongodb["messages"]
            .find({"id": str(target_id)})
            .to_list(length=2)
        )

        if not messages:
            return None

        if len(messages) > 1:
            logging.error(
                f"Multiple messages found with id {target_id}, returning first match"
            )

        return messages[0]

    except Exception as e:
        logging.error(f"Error finding reaction target: {str(e)}")
        return None
