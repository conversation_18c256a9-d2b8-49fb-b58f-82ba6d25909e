from openai import OpenAI
from dotenv import load_dotenv
import os, time, sys
from src.ai_assistants.constants import CHLOE, KATIE
from src.globals import IS_DEV_ENV

if IS_DEV_ENV:
    load_dotenv()

ORG_ID = os.environ.get("OPENAI_ORG_ID")
API_KEY = os.environ.get("OPENAI_API_KEY")

try:
    client = OpenAI(organization=ORG_ID, api_key=API_KEY)
except Exception as e:
    print(f"Failed to initialize OpenAI client: {e}")
    client = None
model = "gpt-4o-mini"


def uprint(*objects, sep=" ", end="\n", file=sys.stdout):
    enc = file.encoding
    if enc == "UTF-8":
        print(*objects, sep=sep, end=end, file=file)
    else:
        f = lambda obj: str(obj).encode(enc, errors="backslashreplace").decode(enc)
        print(*map(f, objects), sep=sep, end=end, file=file)


def wait_on_run(run, thread):
    while run.status == "queued" or run.status == "in_progress":
        run = client.beta.threads.runs.retrieve(
            thread_id=thread.id,
            run_id=run.id,
        )
        time.sleep(0.5)
    return run


def ask_chloe(message: str):
    thread = client.beta.threads.create(
        tool_resources={"file_search": {"vector_store_ids": [CHLOE.store_id]}}
    )
    client.beta.threads.messages.create(
        thread_id=thread.id,
        role="user",
        content=message,
    )
    run = client.beta.threads.runs.create(thread_id=thread.id, assistant_id=CHLOE.id)
    run = wait_on_run(run, thread)
    messages = client.beta.threads.messages.list(thread_id=thread.id)
    # for m in messages:
    #     uprint(m)
    return messages


def ask_katie(message: str):
    thread = client.beta.threads.create(
        tool_resources={"file_search": {"vector_store_ids": [KATIE.store_id]}}
    )
    client.beta.threads.messages.create(
        thread_id=thread.id,
        role="user",
        content=message,
    )
    run = client.beta.threads.runs.create(thread_id=thread.id, assistant_id=KATIE.id)
    run = wait_on_run(run, thread)
    messages = client.beta.threads.messages.list(thread_id=thread.id)
    # for m in messages:
    #     uprint(m)
    return messages


#
# if __name__ == "__main__":
#     print(
#         "Hello! I'm doing wonderfully, thank you for asking! How can I
#         assist you today—perhaps with something fun and interesting about
#         cephalopod care? 🐙")
