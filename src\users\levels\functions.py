from src.users.levels.constants import USER_LEVELS
from src.users.levels.models import UserLevelBaseModel


def get_rank_from_xp(xp: int) -> UserLevelBaseModel:
    # check if they are maxed out
    if xp >= USER_LEVELS[-1].xp_required:
        return USER_LEVELS[-1]

    for index, entry in enumerate(USER_LEVELS):
        if xp >= entry.xp_required and xp < USER_LEVELS[index + 1].xp_required:
            return USER_LEVELS[index]
