from fastapi.testclient import TestClient
from main import app

# from src.messages.models import MessageCreateModel

test_message = {
    "user_id": "2158f687-e086-481a-9124-51d8be81ec87",
    "title": "",
    "content": "TEST TEST TEST",
    "attachment_ids": [""],
    "tags": ["howdy"],
    "forum_id": "tank_building",
    "recipient_id": None,
    "channel_id": None,
    "thread_id": "629689ee-3f1d-11ef-8b53-325096b39f47"}


def test_list_messages():
    with TestClient(app) as client:
        response = client.get("/messages", params={"page_size": 100})
        assert response.status_code == 200


def test_create_message():
    with TestClient(app) as client:
        response = client.post("/message", json=test_message)
        assert response.status_code == 200
        created_msg_id = response.json()["results"]["id"]
        test_message["id"] = created_msg_id


def test_delete_message():
    with Test<PERSON>lient(app) as client:
        response = client.delete("/message",
                                 params={"message_id": test_message["id"]})
        assert response.status_code == 200

# def test_get_user():
#     with TestClient(app) as client:
#         response = client.get("/user",
#                               params={"username": test_user["username"]})
#         assert response.status_code == 200
#         assert response.json()["results"][0]["first_name"] == "Dork"
#
#
# def test_delete_user():
#     with TestClient(app) as client:
#         response = client.post("/user/delete",
#                                params={"username": test_user["username"]})
#         assert response.status_code == 200
