from fastapi.testclient import TestClient
from pprint import pprint
from main import app
from src.forums.constants import FORUMS_LIST


def test_list_forums():
    with TestClient(app) as client:
        response = client.get("/forums/list")
        pprint(response.json())
        assert response.status_code == 200
        assert response.json()["results"] == FORUMS_LIST


def test_get_feed():
    with TestClient(app) as client:
        response = client.get("/forums/feed")
        pprint(response.json())
        assert response.status_code == 200
        # assert response.json()["results"] == FORUMS_LIST
