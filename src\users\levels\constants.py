from src.users.levels.models import (
    UserLevelBaseModel,
    UserLevelTitleDescriptionModel as td,
)

# domain, kingdom, phylum, class, subclass, family, genus, species
# DOMAINS
EUKARYOTA = "Eukaryota"

# KINGDOMS
ANIMALIA = "Animalia"

# PHYLUM
ARTHROPODA = "Arthropoda"
GYRISTA = "Gyrista"
MOLLUSCA = "Mollusca"
MYZOZOA = "Myzozoa"
PLATYHELMINTHES = "Platyhelminthes"

# CLASS
BACILLARIOPHYCEAE = "Bacillariophyceae"
BRANCHIOPODA = "Branchiopoda"
COPEPODA = "Copepoda"
DINOFLAGELLATA = "Dinoflagellata"
GASTROPODA = "Gastropoda"

# SUBCLASS
HETEROBRANCHIA = "Heterobranchia"
SARSOSTRACA = "Sarsostraca"

# FAMILY
PLAKOBRANCHIDAE = "Plakobranchidae"

# GENUS
ELYSIA = "Elysia"

# SPECIES
ELYSIA_CRISPATA = "Elysia Crispa"

level_titles: list[td] = [
    td(
        title="Dinoflagellate",
        description="Smallest of the small, yet one of "
        "the "
        "biggest mariculture problems.",
        classification=[EUKARYOTA, "", MYZOZOA, DINOFLAGELLATA],
    ),
    td(
        title="Diatom",
        description="",
        classification=[EUKARYOTA, "", GYRISTA, BACILLARIOPHYCEAE],
    ),
    td(
        title="Zooxanthellae",
        description="A type of dinoflagellate that have a symbiotic "
        "relationship with corals, jellyfish, and nudibranchs.",
        classification=None,
    ),
    td(
        title="Phytoplankton",
        description="The autotrophs that keep the "
        "world running. They account "
        "for at least half of the global "
        "oxygen production,"
        "despite amounting to only about "
        "1% of the global plant "
        "biomass.  Usually fed to "
        "sponges, clams and other filter "
        "feeders in the aquarium trade.",
        classification=None,
    ),
    td(
        title="Zooplankton",
        description="Heterotrophic plankton, drifting wherever the ocean currents lead.",
        classification=None,
    ),
    td(
        title="Nematocyst",
        description='AKA "cnidocyte".  A stinging cell.  Having this type '
        "of cell is what classifies an animal in the Cnidaria "
        "phylum.",
        classification=None,
    ),
    td(
        title="Tentacle",
        description='A "flexible, mobile, '
        'and elongated organ" some '
        "animals have.  Jellyfish, "
        "anemones, and coral also have "
        "them.",
        classification=None,
    ),
    td(title="Polyp", description="", classification=None),
    td(
        title="Flatworm",
        description="OH NOES! THEY'RE IN... MY TANK!!!",
        classification=[EUKARYOTA, ANIMALIA, PLATYHELMINTHES],
    ),
    td(
        title="Copepod",
        description="Those tiny little crustaceans jumping around on the "
        "aquarium glass or sand. Free food for coral!",
        classification=[EUKARYOTA, ANIMALIA, ARTHROPODA, COPEPODA],
    ),
    td(
        title="Brine Shrimp",
        description="",
        classification=[
            EUKARYOTA,
            ANIMALIA,
            ARTHROPODA,
        ],
    ),
    td(title="Amphipod", description="", classification=None),
    td(title="Berghia Nudibranch", description="", classification=None),
    td(title="Aiptasia", description="", classification=None),
    td(title="Tunicate", description="", classification=None),
    td(title="Sponge", description="", classification=None),
    td(title="Blastomussa", description="", classification=None),
    td(title="Bristleworm", description="", classification=None),
    td(title="Peppermint Shrimp", description="", classification=None),
    td(title="Hermit Crab", description="", classification=None),
    td(title="Discosoma", description="", classification=None),
    td(title="Emerald Crab", description="", classification=None),
    td(
        title="Lettuce Nudibranch",
        description="",
        classification=[
            EUKARYOTA,
            ANIMALIA,
            GASTROPODA,
            HETEROBRANCHIA,
            PLAKOBRANCHIDAE,
            ELYSIA,
            ELYSIA_CRISPATA,
        ],
    ),
    td(
        title="Lace Murex",
        description="A snail that eats other mollusks.  The source of very "
        "expensive purple dye for the ancients.",
        classification=None,
    ),
    td(title="Flame Scallop", description="", classification=None),
    td(title="Clownfish", description="", classification=None),
    td(title="Featherduster Worm", description="", classification=None),
    td(title="Turbo Snail", description="", classification=None),
    td(title="Anemone", description="", classification=None),
    td(
        title="Seahorse",
        description="A panoplied, "
        "monocular-visioned, "
        "prehensile tail "
        "weilding bug eater!",
        classification=None,
    ),
    td(
        title="Moon Jellyfish",
        description='"You ever dance with a ' "jellyfish in the pale " 'moonlight?"',
        classification=None,
    ),
    td(
        title="Tropical Abalone",
        description="Like regular abalone, " "but smaller and in " "warmer waters.",
        classification=None,
    ),
    td(
        title="Sea Urchin",
        description="The spiniest echinoderms in our "
        "tanks, and you can make "
        "them wear little hats!",
        classification=None,
    ),
    td(
        title="Sea Cucumber",
        description="Don't " "let it " "die in " "your " "tank!",
        classification=None,
    ),
    td(
        title="Sand Sifting Starfish",
        description="It's a starfish, that, uh... sifts sand.",
        classification=None,
    ),
    td(
        title="Fighting Conch",
        description="Powerful sand sifters of the " "reef!",
        classification=None,
    ),
    td(
        title="Linckia Starfish",
        description="A six-armed starfish you can throw like a ninja star.",
        classification=None,
    ),
    td(
        title="Sea Hare",
        description="Order: Aplysiida.  Activity: " "EATING ALL THE ALGAE.",
        classification=None,
    ),
    td(
        title="Holy Grail Torch",
        description="A torch coral variety that glows a most brilliant "
        "yellow-green.",
        classification=None,
    ),
    td(
        title="Coral Beauty Angelfish",
        description="A blue Angelfish with "
        "some orange stripes. "
        "Semi-aggressive and "
        "territorial.",
        classification=None,
    ),
    td(
        title="Flame Angelfish",
        description="A brilliant orange-red "
        "Angelfish that are "
        "semi-aggressive and territorial.",
        classification=None,
    ),
    td(
        title="Chocolate Chip Starfish",
        description="A starfish with what looks like, uh, chocolate chips on "
        "it.  Not reef-safe, but amazing nonetheless.",
        classification=None,
    ),
    td(
        title="Serpent Starfish",
        description="Nightmare fuel.  Watch them "
        "hunt down food in the tank.  "
        "Now realize that they have no "
        "brain.",
        classification=None,
    ),
    td(
        title="Nautilus",
        description="The "
        "dumbest of the cephalopods, but are considered "
        '"living fossils". 500 million years of no evolution '
        "means "
        "they "
        "must "
        "be "
        "doing "
        "something right.",
        classification=None,
    ),
    td(
        title="Yellow Tang",
        description="A tang fish, yellow in color.",
        classification=None,
    ),
    td(
        title="Maxima Clam",
        description="Can grow up to 12 inches.",
        classification=None,
    ),
    td(
        title="Purple Tang",
        description="A "
        "purple "
        "fish "
        "with "
        "pinstripe patterns and a yellow tail. "
        " Territorial and ggressive.",
        classification=None,
    ),
    td(title="Lionfish", description="", classification=None),
    td(title="Naso Tang", description="", classification=None),
    td(title="Porcupine Puffer Fish", description="", classification=None),
    td(title="Snowflake moray", description="", classification=None),
    td(title="Reef Squid", description="", classification=None),
    td(title="Scorpion Fish", description="", classification=None),
    td(title="Stonefish", description="", classification=None),
    td(title="Sea Fan", description="", classification=None),
    td(title="Common Octopus", description="", classification=None),
    td(title="Parrotfish", description="", classification=None),
    td(title="Spiny Rock Lobster", description="", classification=None),
    td(title="Horse Conch", description="", classification=None),
    td(title="Giant Isopod", description="", classification=None),
    td(title="Catshark", description="", classification=None),
    td(title="Barracuda", description="", classification=None),
    td(title="Sea Turtle", description="", classification=None),
    td(title="Stingray", description="", classification=None),
    td(title="Moray Eel", description="", classification=None),
    td(title="Nereid", description="", classification=None),
    td(title="Dolphin", description="", classification=None),
    td(title="Oceanid", description="", classification=None),
    td(title="Atlantean", description="", classification=None),
    td(title="Mermaid/Merman", description="", classification=None),
    td(title="Siren", description="", classification=None),
    td(title="Manta Ray", description="", classification=None),
    td(title="Hammerhead Shark", description="", classification=None),
    td(title="Great White Shark", description="", classification=None),
    td(title="Orca", description="", classification=None),
    td(title="Humpback Whale", description="", classification=None),
    td(title="Nessie", description="", classification=None),
    td(title="Vortex Queen", description="", classification=None),
    td(title="Blue Whale", description="", classification=None),
    td(title="Giant Squid", description="", classification=None),
    td(title="Megalodon", description="", classification=None),
    td(title="Leviathan", description="", classification=None),
    td(title="Kraken", description="", classification=None),
    td(title="Charybdis", description="", classification=None),
    td(title="Cthulhu", description="", classification=None),
    td(title="Scylla", description="", classification=None),
    td(title="Jörmungandr", description="", classification=None),
    td(title="Doris", description="", classification=None),
    td(title="Nereus", description="", classification=None),
    td(title="Amphitrite", description="", classification=None),
    td(title="Poseidon", description="", classification=None),
    td(title="Tiamat", description="", classification=None),
    td(title="Oceanus", description="", classification=None),
    td(title="Aegir", description="", classification=None),
    td(title="Njord", description="", classification=None),
    td(title="Proteus", description="", classification=None),
    td(title="Triton", description="", classification=None),
    td(title="Thalassa", description="", classification=None),
    td(title="Ceto", description="", classification=None),
    td(title="Pontus", description="", classification=None),
]

xp_levels = [
    0,
    5,
    15,
    30,
    55,
    95,
    155,
    240,
    360,
    530,
    760,
    1075,
    1505,
    2105,
    2925,
    4045,
    5575,
    7675,
    10545,
    14405,
    19605,
    26605,
    36105,
    49005,
    66505,
    90005,
    121505,
    164005,
    220505,
    296505,
    398505,
    535505,
    719505,
    966505,
    1296505,
    1730505,
    2304505,
    3069505,
    4084505,
    5420505,
    7190505,
    9540505,
    1264505,
    1674505,
    2214505,
    2924505,
    3854505,
    5074505,
    6674505,
    8784505,
    11564505,
    15264505,
    20164505,
    26664505,
    35264505,
    46564505,
    61564505,
    81464505,
    107864505,
    142364505,
    187064505,
    245864505,
    323364505,
    425364505,
    559964505,
    737464505,
    970964505,
    1276954505,
    1686954505,
    2220954505,
    2929954505,
    3879954505,
    5149954505,
    6849954505,
    9119954505,
    12149954505,
    16199954505,
    21599954505,
    28799954505,
    38399954505,
    51199954505,
    68299954505,
    91199954505,
    121599954505,
    162199954505,
    216299954505,
    288399954505,
    384499954505,
    512699954505,
    683999954505,
    911199954505,
    1215999954505,
    1621999954505,
    2162999954505,
    2883999954505,
    3844999954505,
    5126999954505,
    6839999954505,
    9111999954505,
    12159999954505,
]  # 16219999954505

# levels = [
#     {"level": 1, "title": "Novice", "xp_required": 50},
#     {"level": 2, "title": "Apprentice", "xp_required": 150},
#     {"level": 3, "title": "Adept", "xp_required": 300},
#     {"level": 4, "title": "Expert", "xp_required": 550},
#     {"level": 5, "title": "Master", "xp_required": 950},
#     {"level": 6, "title": "Grandmaster", "xp_required": 1550},
#     {"level": 7, "title": "Legend", "xp_required": 2400},
#     {"level": 8, "title": "Epic Hero", "xp_required": 3600},
#     {"level": 9, "title": "Mythical", "xp_required": 5300},
#     {"level": 10, "title": "Immortal", "xp_required": 7600},
#     {"level": 11, "title": "Divine", "xp_required": 10750},
#     {"level": 12, "title": "Celestial", "xp_required": 15050},
#     {"level": 13, "title": "Eternal", "xp_required": 21050},
#     {"level": 14, "title": "Infinite", "xp_required": 29250},
#     {"level": 15, "title": "Transcendent", "xp_required": 40450},
#     {"level": 16, "title": "Supreme", "xp_required": 55750},
#     {"level": 17, "title": "Ultimate", "xp_required": 76750},
#     {"level": 18, "title": "Ascended", "xp_required": 105450},
#     {"level": 19, "title": "Omniscient", "xp_required": 144050},
#     {"level": 20, "title": "Omnipotent", "xp_required": 196050},
#     {"level": 21, "title": "Sage", "xp_required": 266050},
#     {"level": 22, "title": "Oracle", "xp_required": 361050},
#     {"level": 23, "title": "Visionary", "xp_required": 490050},
#     {"level": 24, "title": "Prophet", "xp_required": 665050},
#     {"level": 25, "title": "Messiah", "xp_required": 900050},
#     {"level": 26, "title": "Savior", "xp_required": 1215050},
#     {"level": 27, "title": "Champion", "xp_required": 1640050},
#     {"level": 28, "title": "Hero", "xp_required": 2205050},
#     {"level": 29, "title": "Guardian", "xp_required": 2965050},
#     {"level": 30, "title": "Sentinel", "xp_required": 3985050},
#     {"level": 31, "title": "Warden", "xp_required": 5355050},
#     {"level": 32, "title": "Defender", "xp_required": 7195050},
#     {"level": 33, "title": "Protector", "xp_required": 9665050},
#     {"level": 34, "title": "Vanquisher", "xp_required": 12965050},
#     {"level": 35, "title": "Conqueror", "xp_required": 17305050},
#     {"level": 36, "title": "Victor", "xp_required": 23045050},
#     {"level": 37, "title": "Champion", "xp_required": 30695050},
#     {"level": 38, "title": "Legendary", "xp_required": 40845050},
#     {"level": 39, "title": "Mythical", "xp_required": 54205050},
#     {"level": 40, "title": "Epic", "xp_required": 71905050},
#     {"level": 41, "title": "Supreme", "xp_required": 95405050},
#     {"level": 42, "title": "Ultimate", "xp_required": 12645050},
#     {"level": 43, "title": "Ascended", "xp_required": 16745050},
#     {"level": 44, "title": "Omniscient", "xp_required": 22145050},
#     {"level": 45, "title": "Omnipotent", "xp_required": 29245050},
#     {"level": 46, "title": "Sage", "xp_required": 38545050},
#     {"level": 47, "title": "Oracle", "xp_required": 50745050},
#     {"level": 48, "title": "Visionary", "xp_required": 66745050},
#     {"level": 49, "title": "Prophet", "xp_required": 87845050},
#     {"level": 50, "title": "Messiah", "xp_required": 115645050},
#     {"level": 51, "title": "Savior", "xp_required": 152645050},
#     {"level": 52, "title": "Champion", "xp_required": 201645050},
#     {"level": 53, "title": "Hero", "xp_required": 266645050},
#     {"level": 54, "title": "Guardian", "xp_required": 352645050},
#     {"level": 55, "title": "Sentinel", "xp_required": 465645050},
#     {"level": 56, "title": "Warden", "xp_required": 615645050},
#     {"level": 57, "title": "Defender", "xp_required": 814645050},
#     {"level": 58, "title": "Protector", "xp_required": 1078645050},
#     {"level": 59, "title": "Vanquisher", "xp_required": 1423645050},
#     {"level": 60, "title": "Conqueror", "xp_required": 1870645050},
#     {"level": 61, "title": "Victor", "xp_required": 2458645050},
#     {"level": 62, "title": "Champion", "xp_required": 3233645050},
#     {"level": 63, "title": "Legendary", "xp_required": 4253645050},
#     {"level": 64, "title": "Mythical", "xp_required": 5599645050},
#     {"level": 65, "title": "Epic", "xp_required": 7374645050},
#     {"level": 66, "title": "Supreme", "xp_required": 9709645050},
#     {"level": 67, "title": "Ultimate", "xp_required": 12769545050},
#     {"level": 68, "title": "Ascended", "xp_required": 16869545050},
#     {"level": 69, "title": "Omniscient", "xp_required": 22209545050},
#     {"level": 70, "title": "Omnipotent", "xp_required": 29299545050},
#     {"level": 71, "title": "Sage", "xp_required": 38799545050},
#     {"level": 72, "title": "Oracle", "xp_required": 51499545050},
#     {"level": 73, "title": "Visionary", "xp_required": 68499545050},
#     {"level": 74, "title": "Prophet", "xp_required": 91199545050},
#     {"level": 75, "title": "Messiah", "xp_required": 121499545050},
#     {"level": 76, "title": "Savior", "xp_required": 161999545050},
#     {"level": 77, "title": "Champion", "xp_required": 215999545050},
#     {"level": 78, "title": "Hero", "xp_required": 287999545050},
#     {"level": 79, "title": "Guardian", "xp_required": 383999545050},
#     {"level": 80, "title": "Sentinel", "xp_required": 511999545050},
#     {"level": 81, "title": "Warden", "xp_required": 682999545050},
#     {"level": 82, "title": "Defender", "xp_required": 911999545050},
#     {"level": 83, "title": "Protector", "xp_required": 1215999545050},
#     {"level": 84, "title": "Vanquisher", "xp_required": 1621999545050},
#     {"level": 85, "title": "Conqueror", "xp_required": 2162999545050},
#     {"level": 86, "title": "Victor", "xp_required": 2883999545050},
#     {"level": 87, "title": "Champion", "xp_required": 3844999545050},
#     {"level": 88, "title": "Legendary", "xp_required": 5126999545050},
#     {"level": 89, "title": "Mythical", "xp_required": 6839999545050},
#     {"level": 90, "title": "Epic", "xp_required": 9111999545050},
#     {"level": 91, "title": "Supreme", "xp_required": 12159999545050},
#     {"level": 92, "title": "Ultimate", "xp_required": 16219999545050},
#     {"level": 93, "title": "Ascended", "xp_required": 21629999545050},
#     {"level": 94, "title": "Omniscient", "xp_required": 28839999545050},
#     {"level": 95, "title": "Omnipotent", "xp_required": 38449999545050},
#     {"level": 96, "title": "Sage", "xp_required": 51269999545050},
#     {"level": 97, "title": "Oracle", "xp_required": 68399999545050},
#     {"level": 98, "title": "Visionary", "xp_required": 91119999545050},
#     {"level": 99, "title": "Prophet", "xp_required": 121599999545050},
#     {"level": 100, "title": "Messiah", "xp_required": 162199999545050}
# ]

USER_LEVELS: list[UserLevelBaseModel] = []
for index, title_description in enumerate(level_titles):
    USER_LEVELS.append(
        UserLevelBaseModel(
            rank=index + 1,
            title=title_description.title,
            description=title_description.description,
            classification=title_description.classification,
            xp_required=xp_levels[index],
        )
    )
USER_LEVELS.append(
    UserLevelBaseModel(
        rank=227,
        title="THE VOID",
        description="Heavy lies the crown.",
        classification=None,
        xp_required=99999999999999,
    )
)

# if __name__ == "__main__":
#     print('length: ' + str(len(USER_LEVELS)))
#     print([x for x in
#            USER_LEVELS])
#     # print([round(x[  #  # "xp_required"] / 10) for x
#     # in  #        levels])  # print([x for x in  # level_titles])
