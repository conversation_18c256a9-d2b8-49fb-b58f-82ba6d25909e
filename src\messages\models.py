from datetime import datetime
from pydantic import Field, UUID5, BaseModel
from src.reactions.models import ReactionEnum
from src.media.models.image_models import ImageBaseModel


# what all message forms have in common (eg. user submit/create, full db record, etc)
class MessageBaseModel(BaseModel):
    title: str = Field(...)
    content: str = Field(...)
    user_id: str = UUID5
    images: list[ImageBaseModel] = Field(default_factory=list)
    tags: list[str] | None = Field(...)


""" what users must submit to write/post a message
currently, this is the same as the base model (nothing extra required)"""


class MessageCreateModel(MessageBaseModel):
    pass


# what the backend sends to the frontend for the main feed
# A brief message, w/ related fields (eg user avatar, username, etc.)
class FeedPostApiReturnModel(MessageBaseModel):
    id: str = UUID5
    # fetch the user fields to display
    username: str = Field(...)
    user_avatar_url: str = Field(...)

    # for displaying how long ago message was created
    date_created: datetime = Field(...)

    # number
    num_replies: int
    # list of reactions by type and count
    reactions: dict[ReactionEnum, int] = Field(default_factory=dict)


# what the backend sends to the frontend for the post page
# Full message, w/ related fields (eg user avatar, username, etc.)
class FullPostApiReturnModel(FeedPostApiReturnModel):
    # first 10 replies
    replies: list[FeedPostApiReturnModel] = Field(default_factory=list)


# the full message record; what exists in the database
class MessageRecordModel(MessageBaseModel):
    id: str = UUID5
    date_created: datetime = Field(...)
    last_modified: datetime = Field(...)
    # replies: list[str] | None = Field(...)
    parent_id: str = UUID5
    recipients: list[UUID5] = Field(default_factory=list)
