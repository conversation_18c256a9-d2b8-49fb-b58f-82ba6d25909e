import datetime
from uuid import uuid4
from fastapi.encoders import jsonable_encoder
from src.db.db_wrapper import get_db_client
from src.users.models import UserRecordModel, UserProfileModel, UserSimpleModel
from src.users.roles import UserRoleEnum
from src.users.levels.functions import get_rank_from_xp
from src.charts.models import ColumnChartModel

# from src.auth.functions import get_password_hash
from bson import Int64

from src.auth.authentication import AuthHandler

auth_handler = AuthHandler()

DEFAULT_TOKEN_AMT = 3


def find_user(username: str):
    client = get_db_client()
    try:
        reefDojoDb = client.get_database(name="reefdojo")
        usersCollection = reefDojoDb.get_collection(name="users")
        users = usersCollection.find({"username": username})
        user = users[0]
        return user

    except Exception as e:
        return e.message


def create_full_user_record(**kwargs):
    password_hash = auth_handler.get_password_hash(kwargs.pop("password"))
    today = int(datetime.datetime.now(tz=datetime.timezone.utc).timestamp())
    kwargs["password_hash"] = password_hash
    kwargs["date_joined"] = today
    kwargs["last_login"] = today
    kwargs["roles"] = [UserRoleEnum.FREE]
    kwargs["avatar_link"] = kwargs.get("avatar_link")
    kwargs["tokens"] = kwargs.get("tokens", DEFAULT_TOKEN_AMT)
    kwargs["id"] = str(uuid4())
    # Ensure xp is set (required by UserRecordModel)
    kwargs["xp"] = kwargs.get("xp", 0)

    user_record = UserRecordModel(**kwargs).model_dump()

    # Convert to JSON-compatible format
    encoded_record = jsonable_encoder(user_record)

    # Re-apply Int64 type to timestamp fields after encoding
    encoded_record["date_joined"] = Int64(encoded_record["date_joined"])
    encoded_record["last_login"] = Int64(encoded_record["last_login"])

    return encoded_record


#     client = get_db_client()
#     reefDojoDb = client.get_database(name='reefdojo')
#     usersCollection = reefDojoDb.get_collection(name='users')
#     user = usersCollection.find({'username': user.username})
#     if not user:
#         return None


def get_user_simple_from_user_record(user: UserRecordModel) -> UserSimpleModel:
    user_simple = UserSimpleModel(
        username=user.username,
        avatar_link=user.avatar_link,
        level=get_rank_from_xp(user.xp),
        xp=user.xp,
        roles=user.roles,
        id=user.id,
    )
    return user_simple


def get_user_profile_record_from_user_record(user: UserRecordModel) -> UserProfileModel:
    user_rank = float(get_rank_from_xp(user.xp).rank)
    user_profile = UserProfileModel(
        username=user.username,
        avatar_link=user.avatar_link,
        level=get_rank_from_xp(user.xp),
        xp=user.xp,
        roles=user.roles,
        id=user.id,
        date_joined=user.date_joined,
        profile_chart=ColumnChartModel(
            title="",
            description="",
            categories=["Posts", "Replies", "Voting Power", "Pictures"],
            series=[
                {
                    "data": [
                        12,
                        22,
                        1 if user_rank == 1 else user_rank * 1.5,
                        13,
                    ]
                }
            ],
            x_axis="",
            y_axis="",
        ),
    )
    return user_profile
