# from fastapi.testclient import TestClient
# from pprint import pprint
#
# from main import app
#
#
# # client = TestClient(app)
#
#
# def test_get_user():
#     with TestClient(app) as client:
#         response = client.get("/user", params={"username": "j<PERSON><PERSON>"})
#         assert response.status_code == 200
#         assert response.json()["results"][0][
#                    "first_name"] == "<PERSON>"  # {  #  # "id": "foo",
#         #     "title": "Foo",  #     "description": "There goes  # my hero",
#         # }
#
#
# def test_get_users():
#     with TestClient(app) as client:
#         response = client.get("/users")
#         assert response.status_code == 200  # {  #  # "id": "foo",
#         #     "title": "Foo",  #     "description": "There goes  # my hero",
#         # }
