from fastapi import APIRouter, Request, HTTPException, status, Depends
from src.auth.api import get_current_user
from src.users.models import UserRecordModel
from math import ceil
from src.lib.constants import PAGE_SIZE
from src.messages.models import (
    MessageCreateModel,
    # MessageRecordModel,
    FeedPostApiReturnModel,
    FullPostApiReturnModel,
)
from src.messages.functions import create_full_message_record
from src.db.functions import json_serial
import json

# from src.messages.functions import create_full_message_record
#
messages = APIRouter()


@messages.get("s", response_description="List all messages")
async def list_all_messages(
    request: Request,  # min_price: int = 0,
    # max_price: int = 100000,
    # brand: Optional[str] = None,
    page: int = 1,
    page_size: int = PAGE_SIZE,
):
    skip = (page - 1) * page_size

    # query = {"price": {"$lt": max_price, "$gt": min_price}}
    query = {}
    # if brand:
    #     query["brand"] = brand

    # count total docs
    pages = ceil(
        # await request.app.mongodb["users"].count_documents(query) /
        # RESULTS_PER_PAGE
        await request.app.mongodb["messages"].count_documents(query)
        / page_size  # noqa
    )

    full_query = (
        request.app.mongodb["messages"]
        .find(query)
        .sort("date_created")
        .skip(skip)
        .limit(page_size)
    )

    # Convert MongoDB documents to dictionaries first
    results = []
    async for raw_msg in full_query:
        # Convert ObjectId to string if present
        if "_id" in raw_msg:
            raw_msg["_id"] = str(raw_msg["_id"])

        # Skip model conversion and directly use the document
        # Just ensure all fields are JSON serializable
        serializable_msg = json.loads(json.dumps(raw_msg, default=json_serial))
        results.append(serializable_msg)

    return {"results": results, "pages": pages}


@messages.post("", response_description="Create a new message")
async def create_message(
    request: Request,
    message: MessageCreateModel,
    user: UserRecordModel = Depends(get_current_user),
):
    user_count = await request.app.mongodb["users"].count_documents({"id": user.id})
    # user not found check
    if user_count < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="User not found. " "  Unable to create message.",
        )
    # empty message check
    if len(message.content) < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Empty message content;" " unable to create message.",
        )
    new_msg_record = -1
    try:
        full_message = create_full_message_record(message)
        new_msg_record = await request.app.mongodb["messages"].insert_one(  # noqa
            dict(full_message)
        )
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    retval = full_message["id"]
    return {"results": {"id": retval}}


# TODO: NEED TO ENFORCE AUTHORIZATION ON THIS ONE
@messages.delete("", response_description="Delete a message")
async def delete_message(
    request: Request, message_id: str, user: UserRecordModel = Depends(get_current_user)
):
    message_count = await request.app.mongodb["messages"].count_documents(
        {"id": message_id}
    )
    # user not found check
    if message_count < 1:
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Message not found. " "  Unable to delete message.",
        )
    try:
        result = await request.app.mongodb["messages"].delete_one({"id": message_id})
    except Exception as e:
        raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    if result.deleted_count == 0:
        raise HTTPException(
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No message was deleted.  Internal error.",
        )
    return {"results": {"status": result.acknowledged}}


@messages.get("s/feed", response_description="List feed messages")
async def list_feed_messages(
    request: Request,
    page: int = 1,
    page_size: int = PAGE_SIZE,
):
    skip = (page - 1) * page_size

    query = {"parent_id": "", "recipients": {"$size": 0}}

    # count total records
    pages = ceil(
        # RESULTS_PER_PAGE
        await request.app.mongodb["messages"].count_documents(query)
        / page_size  # noqa
    )
    pipeline = [
        # Match messages with empty parent_id
        {"$match": {"parent_id": ""}},
        # Sort by date_created
        {"$sort": {"date_created": 1}},
        # Skip for pagination
        {"$skip": skip},
        # Limit results
        {"$limit": page_size},
        # Join with users collection
        {
            "$lookup": {
                "from": "users",
                "localField": "user_id",
                "foreignField": "id",
                "as": "user",
            }
        },
        # Unwind the user array
        {"$unwind": "$user"},
        # Add username and avatar_url fields
        {
            "$addFields": {
                "username": "$user.username",
                "user_avatar_url": "$user.avatar_link",
            }
        },
        # Look up reactions for this message
        {
            "$lookup": {
                "from": "reactions",
                "localField": "id",
                "foreignField": "target_id",
                "as": "all_reactions",
            }
        },
        # Count replies (messages with this message's id as parent_id)
        {
            "$lookup": {
                "from": "messages",
                "localField": "id",
                "foreignField": "parent_id",
                "as": "reply_messages",
            }
        },
        {"$addFields": {"num_replies": {"$size": "$reply_messages"}}},
        # Group reactions by type and count into a single object
        {
            "$addFields": {
                "reactions_array": {
                    "$map": {
                        "input": {"$setUnion": ["$all_reactions.reaction"]},
                        "as": "reaction_type",
                        "in": {
                            "k": "$$reaction_type",
                            "v": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_reactions",
                                        "as": "r",
                                        "cond": {
                                            "$eq": ["$$r.reaction", "$$reaction_type"]
                                        },
                                    }
                                }
                            },
                        },
                    }
                }
            }
        },
        # Convert array to object using $arrayToObject
        {"$addFields": {"reactions": {"$arrayToObject": "$reactions_array"}}},
        # Remove temporary array and other fields
        {
            "$project": {
                "user": 0,
                "all_reactions": 0,
                "reactions_array": 0,
                "reply_messages": 0,
            }
        },
    ]
    full_query = (
        request.app.mongodb["messages"].aggregate(pipeline)
        # .find(query)
        # .sort("date_created")
        # .skip(skip)
        # .limit(page_size)
    )

    # Convert MongoDB documents to FeedPostApiReturnModel
    results = []
    async for raw_msg in full_query:
        # Convert ObjectId to string if present
        if "_id" in raw_msg:
            raw_msg["_id"] = str(raw_msg["_id"])

        # Convert raw document to FeedPostApiReturnModel
        feed_message = FeedPostApiReturnModel(**raw_msg)
        results.append(feed_message)

    return {"results": results, "pages": pages}


@messages.get("/{message_id}", response_description="Get a single message")
async def get_message(
    request: Request, message_id: str, user: UserRecordModel = Depends(get_current_user)
):
    # Create an aggregation pipeline to get the message with user info and replies in one query
    pipeline = [
        # Match the requested message
        {"$match": {"id": message_id}},
        # Join with users collection to get author info
        {
            "$lookup": {
                "from": "users",
                "localField": "user_id",
                "foreignField": "id",
                "as": "user",
            }
        },
        {"$unwind": "$user"},
        # Add username and avatar fields
        {
            "$addFields": {
                "username": "$user.username",
                "user_avatar_url": "$user.avatar_link",
            }
        },
        # Look up replies
        {
            "$lookup": {
                "from": "messages",
                "let": {"message_id": "$id"},
                "pipeline": [
                    {"$match": {"$expr": {"$eq": ["$parent_id", "$$message_id"]}}},
                    {"$sort": {"date_created": 1}},
                    {"$limit": PAGE_SIZE},
                    # Join with users collection for reply authors
                    {
                        "$lookup": {
                            "from": "users",
                            "localField": "user_id",
                            "foreignField": "id",
                            "as": "reply_user",
                        }
                    },
                    {"$unwind": "$reply_user"},
                    # Add username and avatar fields to replies
                    {
                        "$addFields": {
                            "username": "$reply_user.username",
                            "user_avatar_url": "$reply_user.avatar_link",
                        }
                    },
                    # Count replies to this reply (for num_replies field)
                    {
                        "$lookup": {
                            "from": "messages",
                            "let": {"reply_id": "$id"},
                            "pipeline": [
                                {
                                    "$match": {
                                        "$expr": {"$eq": ["$parent_id", "$$reply_id"]}
                                    }
                                },
                                {"$count": "count"},
                            ],
                            "as": "reply_count",
                        }
                    },
                    {
                        "$unwind": {
                            "path": "$reply_count",
                            "preserveNullAndEmptyArrays": True,
                        }
                    },
                    {
                        "$addFields": {
                            "num_replies": {"$ifNull": ["$reply_count.count", 0]}
                        }
                    },
                    # Look up reactions for each reply
                    {
                        "$lookup": {
                            "from": "reactions",
                            "localField": "id",
                            "foreignField": "target_id",
                            "as": "all_reactions",
                        }
                    },
                    # Group reactions by type and count
                    {
                        "$addFields": {
                            "reactions_array": {
                                "$map": {
                                    "input": {"$setUnion": ["$all_reactions.reaction"]},
                                    "as": "reaction_type",
                                    "in": {
                                        "k": "$$reaction_type",
                                        "v": {
                                            "$size": {
                                                "$filter": {
                                                    "input": "$all_reactions",
                                                    "as": "r",
                                                    "cond": {
                                                        "$eq": [
                                                            "$$r.reaction",
                                                            "$$reaction_type",
                                                        ]
                                                    },
                                                }
                                            }
                                        },
                                    },
                                }
                            }
                        }
                    },
                    {
                        "$addFields": {
                            "reactions": {"$arrayToObject": "$reactions_array"}
                        }
                    },
                    # Remove unnecessary fields
                    {
                        "$project": {
                            "reply_user": 0,
                            "all_reactions": 0,
                            "reactions_array": 0,
                            "reply_count": 0,
                        }
                    },
                ],
                "as": "replies",
            }
        },
        # Count total replies
        {
            "$lookup": {
                "from": "messages",
                "let": {"message_id": "$id"},
                "pipeline": [
                    {"$match": {"$expr": {"$eq": ["$parent_id", "$$message_id"]}}},
                    {"$count": "count"},
                ],
                "as": "reply_count",
            }
        },
        {"$unwind": {"path": "$reply_count", "preserveNullAndEmptyArrays": True}},
        {"$addFields": {"num_replies": {"$ifNull": ["$reply_count.count", 0]}}},
        # Look up reactions
        {
            "$lookup": {
                "from": "reactions",
                "localField": "id",
                "foreignField": "target_id",
                "as": "all_reactions",
            }
        },
        # Group reactions by type and count
        {
            "$addFields": {
                "reactions_array": {
                    "$map": {
                        "input": {"$setUnion": ["$all_reactions.reaction"]},
                        "as": "reaction_type",
                        "in": {
                            "k": "$$reaction_type",
                            "v": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_reactions",
                                        "as": "r",
                                        "cond": {
                                            "$eq": ["$$r.reaction", "$$reaction_type"]
                                        },
                                    }
                                }
                            },
                        },
                    }
                }
            }
        },
        {"$addFields": {"reactions": {"$arrayToObject": "$reactions_array"}}},
        # Remove temporary fields
        {
            "$project": {
                "user": 0,
                "all_reactions": 0,
                "reactions_array": 0,
                "reply_count": 0,
            }
        },
    ]

    # Execute the aggregation
    result_cursor = request.app.mongodb["messages"].aggregate(pipeline)
    results = [doc async for doc in result_cursor]

    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Message not found"
        )

    message = results[0]

    # Check if user is authorized to view this message
    recipients = message.get("recipients", [])
    has_recipients = len(recipients) > 0
    if message["user_id"] != user.id and (has_recipients and user.id not in recipients):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not authorized to view this message.",
        )

    # Convert ObjectId to string if present
    if "_id" in message:
        message["_id"] = str(message["_id"])

    # Convert reply ObjectIds to strings and ensure they're proper FeedPostApiReturnModel objects
    for reply in message.get("replies", []):
        if "_id" in reply:
            reply["_id"] = str(reply["_id"])

    # Create the FullPostApiReturnModel
    full_post = FullPostApiReturnModel(**message)

    return {"result": full_post}
