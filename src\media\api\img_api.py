from fastapi import API<PERSON>out<PERSON>, File, Request, UploadFile, HTTPException, Depends
from botocore.exceptions import NoCredentialsError, ClientError
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, StreamingResponse
from uuid import uuid4
from src.globals import AWS_S3_BUCKET_NAME, AWS_S3_REGION
from src.users.models import UserRecordModel
from src.auth.api import get_current_user
from src.media.lib.img_functions import get_image_from_key, upload_image_to_s3, get_user_img_dir, edit_image

media_img_router = APIRouter()

@media_img_router.post("/upload/")
async def upload_image(
    request: Request,
    file: UploadFile = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    if not file:
        raise HTTPException(status_code=400, detail="No file provided.")

    img_dir = get_user_img_dir(user)

    upload_image_to_s3(request.app.s3_client, file, img_dir)


@media_img_router.get("/get/{image_key:path}")
async def get_image(
    request: Request,
    image_key: str,
    user: UserRecordModel = Depends(get_current_user),
):
    if not image_key or len(image_key) == 0:
        raise HTTPException(status_code=400, detail="No image key provided.")
    """Get an image by its S3 key. Users can only access their own images."""
    try:
        # Check if the user owns this image (image key should contain user ID)
        if user.id not in image_key:
            raise HTTPException(
                status_code=403, detail="Access denied. You can only access your own images."
            )

        get_image_from_key(request.app.s3_client, image_key)
    except Exception as e:
        raise HTTPException(
                status_code=500, detail=f"Something went awry when trying to get this image.  {e}"
            )

@media_img_router.put("/edit/")
async def edit_image(
    request: Request,
    image_key: str,
    file: UploadFile = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    if not image_key.find(user.id):
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )
    edit_image(request.app.s3_client, file, image_key)


@media_img_router.delete("/delete/")
async def delete_image(
    request: Request, image_key: str, user: UserRecordModel = Depends(get_current_user)
):
    if not image_key.find(user.id):
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )
    response = request.app.s3_client.delete_object(
        Bucket=AWS_S3_BUCKET_NAME, Key=image_key
    )  # noqa
    # pprint(response)
    return {"message": "Image deleted successfully"}


@media_img_router.post("/batch/upload/")
async def batch_upload(
    request: Request,
    files: list[UploadFile] = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    responses = []
    for file in files:
        file_name = get_user_img_dir(user) + str(uuid4()) + (file.filename or "unnamed_file")
        # s3_key = f"{datetime.now(datetime.UTC).isoformat()}-{uuid4()}.jpg"
        # request.app.s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, s3_key)
        request.app.s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, file_name)
        responses.append({"key": file_name})

    # TODO: Remove this before going live!
    return {"message": "Batch upload successful", "files": responses}


@media_img_router.post("/batch/delete/")
async def batch_delete(
    request: Request, keys: list[str], user: UserRecordModel = Depends(get_current_user)
):
    objects = [{"Key": key} for key in keys]
    if [not key.find(user.id) for key in keys]:
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )

    request.app.s3_client.delete_objects(
        Bucket=AWS_S3_BUCKET_NAME, Delete={"Objects": objects}
    )
    return {"message": "Batch delete successful"}


# if __name__ == "__main__":
# print(request.app.s3_client.profile_name)
# print(str(request.app.s3_client.list_buckets()))
